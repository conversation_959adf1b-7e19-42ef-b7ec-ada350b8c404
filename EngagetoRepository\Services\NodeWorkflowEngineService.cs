using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoEntities.Constants;
using EngagetoRepository.MetaServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Text;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoEntities.Dtos.AutomationDtos;
using Mapster;
using EngagetoEntities.Dtos.TemplateDtos;
using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Asn1.X509;
using Microsoft.IdentityModel.Tokens;
using System.Linq;
using EngagetoContracts.Services;
using EngagetoContracts.AttributeName;
using EngagetoContracts.WorkflowRepository;
using EngagetoEntities.Dtos.AttributeNameDtos;
using DocumentFormat.OpenXml.Bibliography;
using EngagetoEntities.Dtos.WorkflowDtos;
using EngagetoEntities.Dtos.MetaDto;
using Microsoft.AspNetCore.Http;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities;
using EngagetoRepository.TemplateRepository;
using Microsoft.Extensions.Configuration;
using Quartz.Util;
using System.Threading;
using System.Text;
using EngagetoContracts.GeneralContracts;

namespace EngagetoRepository.Services
{
    public class NodeWorkflowEngineService : INodeWorkflowEngineService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<NodeWorkflowEngineService> _logger;
        private readonly IMetaApiService _metaApiService;
        private readonly IGenericRepository _genericRepository;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IWorkflowCustomResponseService _workflowCustomResponseService;
        private readonly IAttributeNameService _attributeNameService;
        private readonly ICustomerWorkflowTrackerRepository _customerWorkflowTrackerRepository;
        private readonly IUserIdentityService _userIdentityService;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IEnvironmentService _environmentService;
        private readonly IConfiguration _configuration;
        // Removed ITemplate from constructor to avoid circular dependency
        // Will use IServiceProvider to resolve ITemplate at runtime

        public NodeWorkflowEngineService(
            ApplicationDbContext dbContext,
            ILogger<NodeWorkflowEngineService> logger,
            IMetaApiService metaApiService,
            IGenericRepository genericRepository,
            IHttpClientFactory httpClientFactory,
            IServiceProvider serviceProvider,
            IWorkflowCustomResponseService workflowCustomResponseService,
            IAttributeNameService attributeNameService,
            ICustomerWorkflowTrackerRepository customerWorkflowTrackerRepository,
            IUserIdentityService userIdentityService,
            ILogHistoryService logHistoryService,
            IEnvironmentService environmentService,
            IConfiguration configuration
            )
        {
            _dbContext = dbContext;
            _logger = logger;
            _metaApiService = metaApiService;
            _genericRepository = genericRepository;
            _httpClientFactory = httpClientFactory;
            _serviceProvider = serviceProvider;
            _workflowCustomResponseService = workflowCustomResponseService;
            _attributeNameService = attributeNameService;
            _customerWorkflowTrackerRepository = customerWorkflowTrackerRepository;
            _userIdentityService = userIdentityService;
            _logHistoryService = logHistoryService;
            _environmentService = environmentService;
            _configuration = configuration;
        }

        public async Task ProcessWorkflowAsync(Contacts contact, string? textMessage, bool? isNewCustomer = false, bool? isStatusChange = false, bool? isProjectChange = false)
        {
            var startTime = DateTime.UtcNow;

            try
            {

                var nodeStartTime = DateTime.UtcNow;
                var currentNode = await GetStartingNodeAsync(contact, textMessage, isNewCustomer, isStatusChange, isProjectChange);
                if (currentNode == null)
                {
                    return;
                }

                var processStartTime = DateTime.UtcNow;
                var nextNodeId = await ProcessNodeAsync(currentNode, contact, textMessage);

                if (nextNodeId != null)
                {
                    contact.WorkFlowNodeId = nextNodeId;
                    contact.WorkflowId = currentNode.WorkflowId;
                    _dbContext.Contacts.Update(contact);
                    await _dbContext.SaveChangesAsync();

                    await ProcessWorkflowAsync(contact, null, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing workflow for customer {CustomerId}", contact.ContactId);
            }
            finally
            {
                _logger.LogInformation("Total workflow processing time: {Duration}ms",
                    (DateTime.UtcNow - startTime).TotalMilliseconds);
            }
        }

        private async Task<WorkflowNode?> GetStartingNodeAsync(Contacts contact, string? textMessage, bool? isNewCustomer, bool? isStatusChange = false, bool? isProjectChange = false)
        {
            try
            {
                if (contact.WorkFlowNodeId != null && contact.WorkFlowNodeId != Guid.Empty)
                {
                    var activeNode = await _dbContext.WorkflowNodes
                        .FirstOrDefaultAsync(n => n.Id == contact.WorkFlowNodeId);

                    if (activeNode != null)
                    {
                        return activeNode;
                    }
                    else
                    {
                        contact.WorkFlowNodeId = null;
                    }
                }

                if (isStatusChange == true)
                {
                    try
                    {
                        var statusWorkflows = await _dbContext.WorkflowNodes
                            .Include(n => n.Workflow)
                            .Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted && n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();


                        var matchingWorkflows = statusWorkflows
                            .Where(n =>
                                n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.Status &&
                                n.PayloadModel?.FlowStartModel?.LeadStatus != null &&
                                n.PayloadModel.FlowStartModel.LeadStatus.Any(ls =>
                                    ls.Status?.ToLower() == contact.LeadStatus?.ToString()?.ToLower() &&
                                    (
                                        ls.SubStatus == null || ls.SubStatus.Count == 0 || string.IsNullOrEmpty(contact.LeadSubStatus?.ToString()) ||
                                        (ls.SubStatus != null && ls.SubStatus.Contains(contact.LeadSubStatus.ToString()))
                                    ))).ToList();



                        if (matchingWorkflows.Any())
                        {
                            var selectedNode = matchingWorkflows
                                .OrderByDescending(n => n.CreatedAt)
                                .FirstOrDefault();

                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;

                                await _dbContext.SaveChangesAsync();
                                return selectedNode;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing status change workflow for contact {ContactId}", contact.ContactId);
                        throw;
                    }
                }

                if (isProjectChange == true)
                {
                    try
                    {
                        var projectWorkflows = await _dbContext.WorkflowNodes
                                             .Include(n => n.Workflow)
                                              .Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted && n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();


                        var matchingWorkflows = projectWorkflows
                                              .Where(n => n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.Project &&
                                                         n.PayloadModel?.FlowStartModel?.LeadProject != null &&
                                                         n.PayloadModel.FlowStartModel.LeadProject.Any(ls =>
                                                         ls?.ToLower() == contact.Project?.ToString()?.ToLower())).ToList();

                        if (matchingWorkflows.Any())
                        {
                            var selectedNode = matchingWorkflows
                                .OrderByDescending(n => n.CreatedAt)
                                .FirstOrDefault();

                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;

                                await _dbContext.SaveChangesAsync();
                                return selectedNode;
                            }
                        }


                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing project change workflow for contact {ContactId}", contact.ContactId);
                        throw;
                    }

                }

                if (isNewCustomer == true)
                {
                    var potentialNodes = await _dbContext.WorkflowNodes
                                       .Include(n => n.Workflow).Where(n => n.Type == NodeType.FlowStart && n.Workflow.IsActive && !n.Workflow.IsDeleted &&
                                        n.Workflow.CompanyId == contact.BusinessId.ToString()).ToListAsync();

                    var sourceMatchingNodes = potentialNodes
                        .Where(n =>
                            n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead &&
                            n.PayloadModel?.FlowStartModel?.LeadSource != null &&
                            n.PayloadModel.FlowStartModel.LeadSource.Any(ls =>
                                ls.Source == contact.Source.ToString() &&
                                (
                                    string.IsNullOrEmpty(contact.SubSource?.ToString()) || ls.SubSource != null && ls.SubSource.Contains(contact.SubSource.ToString())
                                ))).ToList();



                    if (sourceMatchingNodes.Any())
                    {
                        var selectedNode = SelectPriorityWorkflow(sourceMatchingNodes);
                        contact.WorkflowId = selectedNode.WorkflowId;
                        return selectedNode;
                    }

                    var allDefaultNodes = await _dbContext.WorkflowNodes
                       .Include(n => n.Workflow)
                       .Where(n => n.Type == NodeType.FlowStart &&
                                  n.IsEntry &&
                                  n.Workflow.IsActive &&
                                  !n.Workflow.IsDeleted &&
                                  n.Workflow.CompanyId == contact.BusinessId.ToString())
                       .ToListAsync();

                    var defaultNodes = allDefaultNodes
                        .Where(n => n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead)
                        .ToList();

                    if (defaultNodes.Any())
                    {
                        var selectedNode = SelectPriorityWorkflow(defaultNodes);
                        contact.WorkflowId = selectedNode.WorkflowId;

                        return selectedNode;
                    }
                    return null;
                }

                if (!string.IsNullOrEmpty(textMessage) || !string.IsNullOrWhiteSpace(textMessage))
                {

                    var matchingKeywords = await _dbContext.WorkflowKeywords
                       .Include(k => k.Workflow)
                       .Where(k => !k.IsDeleted &&
                            k.BusinessId == contact.BusinessId.ToString() &&
                            k.Keyword.ToLower() == textMessage.ToLower() &&
                            k.Workflow.IsActive)
                       .Select(k => new
                       {
                           k.WorkflowId,
                           WorkflowNodeId = Guid.Parse(k.WorkflowNodeId.ToString()),
                           k.Keyword,
                           k.Workflow
                       })
                       .ToListAsync();

                    if (matchingKeywords.Any())
                    {
                        // Get the matching nodes directly using WorkflowNodeId
                        var matchingNodes = await _dbContext.WorkflowNodes
                            .Where(n => matchingKeywords.Select(k => k.WorkflowNodeId).Contains(n.Id) && n.Type == NodeType.FlowStart && !n.IsDeleted).ToListAsync();

                        if (matchingNodes.Any())
                        {
                            var selectedNode = SelectPriorityWorkflow(matchingNodes);
                            if (selectedNode != null)
                            {
                                contact.WorkflowId = selectedNode.WorkflowId;
                                contact.WorkFlowNodeId = selectedNode.Id;
                                return selectedNode;
                            }
                        }
                    }

                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error finding starting node for customer {CustomerId}",
                    contact.ContactId);
                return null;
            }
        }
        private WorkflowNode SelectPriorityWorkflow(List<WorkflowNode> nodes)
        {
            if (nodes == null || !nodes.Any())
                throw new ArgumentException("No nodes provided for selection");

            var byCreationDate = nodes.OrderByDescending(n => n.CreatedAt).FirstOrDefault();
            if (byCreationDate != null)
                return byCreationDate;

            return nodes.First();
        }

        private async Task<Guid?> ProcessNodeAsync(WorkflowNode node, Contacts contact, string? textMessage)
        {

            try
            {
                Guid? nextNode = null;

                switch (node.Type)
                {
                    case NodeType.FlowStart:
                        nextNode = await ProcessFlowStartNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.InteractiveMessage:
                        nextNode = await ProcessInteractiveMessageNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.HttpRequest:
                        nextNode = await ProcessHttpRequestNodeAsync(node, contact);
                        break;
                    case NodeType.Template:
                        nextNode = await ProcessTemplateNodeAsync(node, contact, textMessage);
                        break;
                    case NodeType.Condition:
                        nextNode = await ProcessConditionNodeAsync(node, contact, textMessage);
                        break;
                    default:
                        _logger.LogWarning("Unknown node type: {NodeType}", node.Type);
                        break;
                }
                if (nextNode != null)
                {
                    return nextNode;
                }
                var hasOutgoingEdges = await _dbContext.WorkflowEdges.AnyAsync(e => e.SourceId == node.Id);

                if (!hasOutgoingEdges)
                {
                    await MarkWorkflowCompletedAsync(contact);
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }

        }
        private async Task MarkWorkflowCompletedAsync(Contacts contact)
        {
            try
            {
                var activeTrackers = await _dbContext.CustomerWorkflowTrackers
              .Where(t =>
                t.ContactId == contact.ContactId &&
                t.WorkflowId == contact.WorkflowId &&
               !t.CompletedAt.HasValue)
               .ToListAsync();
                if (activeTrackers.Any())
                {
                    foreach (var tracker in activeTrackers)
                    {
                        tracker.CompletedAt = DateTime.UtcNow;
                        _dbContext.CustomerWorkflowTrackers.Update(tracker);
                    }
                }
                contact.WorkflowId = null;
                contact.WorkFlowNodeId = null;
                _dbContext.Contacts.Update(contact);

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {

            }
        }
        private async Task<Guid?> ProcessFlowStartNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                if (edge == null || !edge.Targets.Any())
                {
                    return null;
                }
                var nextNodeId = edge.Targets.First().TargetNodeId;

                return nextNodeId;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task<Guid?> ProcessInteractiveMessageNodeAsync(WorkflowNode node, Contacts contact, string? textMessage)
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var interactiveMessage = node.PayloadModel?.InteractiveMessage;
                if (interactiveMessage == null)
                {
                    _logger.LogWarning("Interactive message payload is null for node {NodeId}", node.Id);
                    return null;
                }
                var customerResponse = await _workflowCustomResponseService.GetWorkflowCustomResponseAsync(
                    node.Id,
                    node.WorkflowId,
                    contact.BusinessId
                );

                string? attributeName = null;
                if (customerResponse != null && customerResponse.AttributeId.HasValue)
                {
                    attributeName = await _attributeNameService.GetAttributeNameByIdAsync(customerResponse.AttributeId.Value);
                }

                if (!string.IsNullOrEmpty(textMessage))
                {
                    // **CANCEL TIMEOUT WHEN USER RESPONDS**
                    await CancelTimeoutIfNeeded(contact, node.Id, "Interactive Message");

                    var existingTracker = await _dbContext.CustomerWorkflowTrackers
                     .FirstOrDefaultAsync(t =>
                         t.ContactId == contact.ContactId &&
                         t.WorkflowId == node.WorkflowId &&
                         t.NodeId == node.Id &&
                         t.CompletedAt == null);

                    if (existingTracker != null)
                    {
                        existingTracker.CustomerReponse = textMessage;
                        existingTracker.AttributeName = attributeName;
                        existingTracker.UpdatedAt = DateTime.UtcNow;

                        _dbContext.CustomerWorkflowTrackers.Update(existingTracker);
                    }
                    else
                    {
                        var tracker = new CustomerWorkflowTracker
                        {
                            Id = Guid.NewGuid(),
                            BusinessId = contact.BusinessId,
                            ContactId = contact.ContactId,
                            WorkflowId = node.WorkflowId,
                            NodeId = node.Id,
                            NodeType = node.Type,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CustomerReponse = textMessage,
                            AttributeName = attributeName
                        };
                        await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);
                    }
                    try
                    {
                        await _dbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                    }

                    var edges = await _dbContext.WorkflowEdges
                        .Include(e => e.Targets)
                        .Where(e => e.SourceId == node.Id)
                        .ToListAsync();

                    if (!edges.Any())
                    {
                        return null;
                    }

                    var targets = edges.SelectMany(e => e.Targets).ToList();

                    if (!targets.Any())
                    {
                        return null;
                    }

                    if (targets.Count == 1)
                    {
                        return targets[0].TargetNodeId;
                    }

                    if (targets.Count > 1)
                    {
                        foreach (var target in targets)
                        {
                            if (string.IsNullOrEmpty(target.Condition))
                                continue;

                            var conditionParts = target.Condition.Split(new[] { "==" }, StringSplitOptions.RemoveEmptyEntries);
                            if (conditionParts.Length != 2)
                                continue;

                            var expectedValue = conditionParts[1].Trim().Trim('\'', '"');

                            if (textMessage.Equals(expectedValue, StringComparison.OrdinalIgnoreCase))
                            {
                                var targetNode = await _dbContext.WorkflowNodes.FindAsync(target.TargetNodeId);
                                return target.TargetNodeId;
                            }
                        }
                        var defaultTarget = targets.FirstOrDefault(t => string.IsNullOrEmpty(t.Condition));
                        if (defaultTarget != null)
                        {
                            return defaultTarget.TargetNodeId;
                        }
                    }
                    return targets[0].TargetNodeId;
                }
                else
                {
                    var existingTracker = await _dbContext.CustomerWorkflowTrackers
                   .FirstOrDefaultAsync(t =>
                    t.ContactId == contact.ContactId &&
                    t.WorkflowId == node.WorkflowId &&
                    t.NodeId == node.Id &&
                    t.CompletedAt == null);

                    if (existingTracker != null)
                    {
                        existingTracker.CustomerReponse = textMessage;
                        existingTracker.AttributeName = attributeName;
                        existingTracker.UpdatedAt = DateTime.UtcNow;

                        _dbContext.CustomerWorkflowTrackers.Update(existingTracker);
                        await _dbContext.SaveChangesAsync();
                    }
                    else
                    {
                        var tracker = new CustomerWorkflowTracker
                        {
                            Id = Guid.NewGuid(),
                            BusinessId = contact.BusinessId,
                            ContactId = contact.ContactId,
                            WorkflowId = node.WorkflowId,
                            NodeId = node.Id,
                            NodeType = node.Type,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CustomerReponse = textMessage,
                            AttributeName = attributeName
                        };

                        await _dbContext.CustomerWorkflowTrackers.AddAsync(tracker);

                        try
                        {
                            await _dbContext.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                    var account = await _dbContext.BusinessDetailsMetas
                        .FirstOrDefaultAsync(x => x.BusinessId == contact.BusinessId.ToString());

                    if (account == null)
                    {
                        return null;
                    }

                    var phoneNumber = $"{contact.CountryCode}{contact.Contact}";

                    var bodyMessage = await ProcessInteractiveMessageVariablesAsync(interactiveMessage, contact);

                    try
                    {
                        if (interactiveMessage.Type == InteractiveType.Button && interactiveMessage.Buttons != null && interactiveMessage.Buttons.Any())
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, interactiveMessage.Header, interactiveMessage.Footer, interactiveMessage.mediaType, interactiveMessage.mediaFile, interactiveMessage.Type, interactiveMessage.Buttons, null);

                            string actionJson = JsonConvert.SerializeObject(new
                            {
                                type = "button",
                                buttons = interactiveMessage.Buttons.Select(b => new
                                {
                                    type = "reply",
                                    reply = new
                                    {
                                        id = b.Id,
                                        title = b.Name
                                    }
                                }).ToList()
                            });

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                BusinessId = Guid.TryParse(account.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty,
                                ContactId = contact.ContactId,
                                MessageType = MessageType.Interactive,
                                TextMessage = bodyMessage,
                                Action = null, // Don't use Action for Quick Reply style buttons
                                TemplateHeader = interactiveMessage.Header,
                                TemplateMediaType = interactiveMessage.mediaType,
                                TemplateMediaUrl = interactiveMessage.mediaFile,
                                TemplateBody = bodyMessage,
                                TemplateFooter = interactiveMessage.Footer,
                                QuickReplies = String.Join(",", interactiveMessage.Buttons.Select(b => b.Name)), // Use QuickReplies for bottom button style
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            if (!response.IsSuccess)
                            {
                                contact.WorkFlowNodeId = null;
                                contact.WorkflowId = null;
                                _dbContext.Contacts.Update(contact);
                            }
                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();
                        }
                        else if (interactiveMessage.Type == InteractiveType.List && interactiveMessage.List != null)
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, interactiveMessage.Header, interactiveMessage.Footer, interactiveMessage.mediaType, interactiveMessage.mediaFile, interactiveMessage.Type, null, interactiveMessage.List);

                            string actionJson = JsonConvert.SerializeObject(new
                            {
                                type = "list",
                                button = interactiveMessage.List.ButtonText,
                                sections = interactiveMessage.List.Sections.Select(s => new
                                {
                                    title = s.Title,
                                    rows = s.Rows.Select(r => new
                                    {
                                        id = r.Id,
                                        title = r.Title,
                                        description = r.Description
                                    }).ToList()
                                }).ToList()
                            });

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                BusinessId = Guid.TryParse(account.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty,
                                ContactId = contact.ContactId,
                                MessageType = MessageType.Interactive,
                                TextMessage = bodyMessage,
                                Action = actionJson,
                                TemplateHeader = interactiveMessage.Header,
                                TemplateMediaType = interactiveMessage.mediaType,
                                TemplateBody = bodyMessage,
                                TemplateFooter = interactiveMessage.Footer,
                                QuickReplies = null,
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            if (!response.IsSuccess)
                            {
                                contact.WorkFlowNodeId = null;
                                contact.WorkflowId = null;
                                _dbContext.Contacts.Update(contact);
                            }
                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();

                        }
                        else
                        {
                            var response = await _metaApiService.SendInteractiveMessageAsync(account.BusinessId, phoneNumber, bodyMessage, null, null, interactiveMessage.mediaType, null, interactiveMessage.Type, null, null);

                            var conv = new Conversations
                            {
                                Id = Guid.NewGuid(),
                                From = account.BusinessId,
                                To = phoneNumber,
                                BusinessId = Guid.TryParse(account.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty,
                                ContactId = contact.ContactId,
                                MessageType = MessageType.Normal,
                                TextMessage = bodyMessage,
                                CreatedAt = DateTime.UtcNow,
                                Status = response.IsSuccess ? ConvStatus.sent : ConvStatus.failed,
                                WhatsAppMessageId = StringHelper.GetValueFromResponse(response.Result, "messages[0].id") ?? string.Empty,
                                ErrorMessage = response.IsSuccess ? null : StringHelper.GetValueFromResponse(response.Result, "error.message"),
                                ErrorDetails = response.IsSuccess ? null : JsonConvert.SerializeObject(response.Result)
                            };

                            if (!response.IsSuccess)
                            {
                                contact.WorkFlowNodeId = null;
                                contact.WorkflowId = null;
                                _dbContext.Contacts.Update(contact);
                            }
                            await _dbContext.Conversations.AddAsync(conv);
                            await _dbContext.SaveChangesAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);

                    }
                    await HandleTimeout(node, contact, interactiveMessage.IsEnbaleSetTimeOut, interactiveMessage.TimeOut, "Interactive Message");
                }

                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        private string ResolveVariableValue(string variable, string? userResponse, string? variableValue, string? fallbackValue, Contacts? contact = null)
        {
            if (!string.IsNullOrEmpty(userResponse))
            {
                return userResponse;
            }

            if (contact != null && !string.IsNullOrEmpty(variableValue))
            {
                var propertyInfo = typeof(Contacts).GetProperty(variableValue);
                if (propertyInfo != null)
                {
                    var value = propertyInfo.GetValue(contact);
                    if (value != null)
                    {
                        if (variableValue.Equals("Contact", StringComparison.OrdinalIgnoreCase))
                        {
                            return $"{contact.CountryCode}{value}";
                        }
                        return value.ToString();
                    }
                }
            }


            if (!string.IsNullOrEmpty(variableValue))
            {
                return variableValue;
            }

            return fallbackValue ?? "Unknown";
        }

        private async Task<string> ReplaceVariablesInJsonBody(string jsonBody, List<VariableModel> variableValues, Contacts contact)
        {
            try
            {
                if (jsonBody.StartsWith("\"") && jsonBody.EndsWith("\""))
                {
                    jsonBody = JsonConvert.DeserializeObject<string>(jsonBody);
                }

                foreach (var variable in variableValues)
                {
                    if (!string.IsNullOrEmpty(variable.Variable))
                    {
                        var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contact.ContactId, contact.WorkflowId);

                        var matchingTracker = previousResponses.FirstOrDefault(t => t.AttributeName != null && t.AttributeName.Equals(variable.Value, StringComparison.OrdinalIgnoreCase));

                        string replacementValue = ResolveVariableValue(variable.Variable, matchingTracker?.CustomerReponse, variable.Value, variable.FallbackValue, contact);
                        jsonBody = jsonBody.Replace(variable.Variable, replacementValue);
                    }
                }
                var parsedJson = JsonConvert.DeserializeObject(jsonBody);
                return JsonConvert.SerializeObject(parsedJson);
            }
            catch (Exception ex)
            {
                return jsonBody;
            }
        }

        private async Task<string> ProcessInteractiveMessageVariablesAsync(InteractiveMessageModel interactiveMessage, Contacts contact)
        {
            try
            {
                var messageBody = interactiveMessage.Body;
                var variables = interactiveMessage.Variables;

                if (variables == null || !variables.Any())
                {
                    return messageBody;
                }
                var hasLeadratVariables = variables.Any(v => StringHelper.IsLeadratVariable(v.Variable));

                if (!hasLeadratVariables)
                {
                    return await ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                }
                else
                {
                    var (updatedMessage, extractedVariables) = StringHelper.ReplaceAndExtractVariables(messageBody);
                    return await ProcessInteractiveMessageWithLeadratVariablesAsync(updatedMessage, variables, extractedVariables, contact);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing interactive message variables, using fallback");
                return await ProcessInteractiveMessageWithLocalVariables(interactiveMessage.Body, interactiveMessage.Variables, contact);
            }
        }
        private async Task<string> ProcessInteractiveMessageWithLocalVariables(string messageBody, List<VariableModel> variables, Contacts contact)
        {
            try
            {
                if (variables == null || !variables.Any())
                {
                    return messageBody;
                }

                var contactProperties = typeof(Contacts).GetProperties().ToDictionary(p => p.Name, p => p.GetValue(contact)?.ToString() ?? string.Empty);
                var variableValuesList = new List<string>();

                var sortedVariables = variables
                    .Where(v => v.Variable.StartsWith("{{") && v.Variable.EndsWith("}}"))
                    .OrderBy(v =>
                    {
                        var numberStr = v.Variable.Trim('{', '}');
                        return int.TryParse(numberStr, out var number) ? number : int.MaxValue;
                    })
                    .ToList();

                foreach (var variable in sortedVariables)
                {
                    //var value = contactProperties.ContainsKey(variable.Value) && !string.IsNullOrEmpty(contactProperties[variable.Value])
                    //    ? contactProperties[variable.Value]
                    //    : variable.FallbackValue;
                    //variableValuesList.Add(value);

                    string value = string.Empty;

                    if (contactProperties.TryGetValue(variable.Value, out var contactValue) && !string.IsNullOrEmpty(contactValue))
                    {
                        if (Guid.TryParse(contactValue, out var userId))
                        {
                            var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
                            value = user?.Name ?? variable.FallbackValue;
                        }
                        else
                        {
                            value = contactValue;
                        }
                    }
                    else
                    {
                        value = variable.FallbackValue;
                    }
                    variableValuesList.Add(value);
                }

                return StringHelper.ReplacePlaceholders(messageBody, variableValuesList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing local variables");
                return messageBody;
            }
        }
        private async Task<string> ProcessInteractiveMessageWithLeadratVariablesAsync(string messageBody, List<VariableModel> variables, List<string> extractedVariables, Contacts contact)
        {
            try
            {
                var businessDetails = await _dbContext.BusinessDetails.FirstOrDefaultAsync(i => i.Id == contact.BusinessId);
                LeadratApiUrls.SetEnvironment(_environmentService.IsDevelopment);

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Add("tenant", businessDetails.TenantId);

                var leadratResponse = await httpClient.GetAsync(LeadratApiUrls.GetLeadApiUrl(contact.Contact, contact.CountryCode));

                if (!leadratResponse.IsSuccessStatusCode)
                {
                    return await ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
                }

                var leadratJsonString = await leadratResponse.Content.ReadAsStringAsync();
                var leadratDataDict = LeadRatMapping.ConvertApiResponseToDictionary(leadratJsonString);

                Dictionary<string, object> userDataDict = new Dictionary<string, object>();
                var assignmentIdValue = LeadRatMapping.GetValueFromApiData(leadratDataDict, "data.assignTo", null, _logger) ??
                                       LeadRatMapping.GetValueFromApiData(leadratDataDict, "assignTo", null, _logger);

                if (!string.IsNullOrEmpty(assignmentIdValue) && Guid.TryParse(assignmentIdValue, out var assignmentId))
                {
                    try
                    {
                        var userResponse = await httpClient.GetAsync(LeadratApiUrls.GetUserDetailsApiUrl(assignmentId));
                        if (userResponse.IsSuccessStatusCode)
                        {
                            var userJsonString = await userResponse.Content.ReadAsStringAsync();
                            userDataDict = LeadRatMapping.ConvertApiResponseToDictionary(userJsonString);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "User API call failed, continuing with Leadrat data only");
                    }
                }

                var extractedVariableModels = new List<VariableModel>();
                foreach (var extractedVar in extractedVariables)
                {
                    var matchingVariable = variables.FirstOrDefault(v => v.Variable == extractedVar);
                    if (matchingVariable != null)
                    {
                        extractedVariableModels.Add(new VariableModel
                        {
                            Variable = extractedVar,
                            FallbackValue = matchingVariable.FallbackValue
                        });
                    }
                    else
                    {
                        extractedVariableModels.Add(new VariableModel
                        {
                            Variable = extractedVar,
                            FallbackValue = extractedVar
                        });
                    }
                }
                var processedMessage = LeadRatMapping.ProcessLeadRatVariables(leadratDataDict, userDataDict, extractedVariableModels, messageBody, _logger);
                return processedMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing LeadRat variables for contact {ContactId}", contact.ContactId);
                return await ProcessInteractiveMessageWithLocalVariables(messageBody, variables, contact);
            }
        }

        private async Task<Guid?> ProcessHttpRequestNodeAsync(WorkflowNode node, Contacts contacts)
        {
            var httpRequest = node.PayloadModel.HttpRequest;
            if (httpRequest == null)
            {
                return null;
            }
            try
            {
                var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contacts.ContactId, contacts.WorkflowId);
                var variableValues = new List<VariableModel>();
                if (httpRequest.VariableValues != null)
                {
                    foreach (var variable in httpRequest.VariableValues)
                    {
                        var matchingTracker = previousResponses
                         .FirstOrDefault(t => t.AttributeName != null &&
                                  t.AttributeName.Equals(variable.Value, StringComparison.OrdinalIgnoreCase));

                        var variableModel = new VariableModel
                        {
                            Variable = variable.Variable,
                            Value = matchingTracker?.CustomerReponse ?? variable.Value,
                            FallbackValue = variable.FallbackValue
                        };
                        variableValues.Add(variableModel);
                    }
                }

                string processedJsonBody = httpRequest.JsonBody;
                if (!string.IsNullOrEmpty(processedJsonBody))
                {
                    processedJsonBody = await ReplaceVariablesInJsonBody(processedJsonBody, variableValues, contacts);
                }
                using var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromSeconds(30);

                var request = new HttpRequestMessage(
                    new HttpMethod(httpRequest.Method),
                    httpRequest.Url
                );

                if (httpRequest.Headers != null)
                {
                    foreach (var header in httpRequest.Headers)
                    {
                        if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                            continue;

                        request.Headers.Add(header.Key, header.Value.ToString());
                    }
                }
                if (httpRequest.QueryParameters != null)
                {
                    var queryString = string.Join("&",
                        httpRequest.QueryParameters.Select(kvp =>
                            $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value.ToString())}"));

                    if (!string.IsNullOrEmpty(queryString))
                    {
                        request.RequestUri = new Uri($"{httpRequest.Url}?{queryString}");
                    }
                }

                if (!string.IsNullOrEmpty(processedJsonBody))
                {
                    request.Content = new StringContent(
                        processedJsonBody,
                        Encoding.UTF8,
                        "application/json"
                    );
                }
                else if (httpRequest.FormData != null && httpRequest.FormData.Any())
                {
                    var formContent = new MultipartFormDataContent();
                    foreach (var item in httpRequest.FormData)
                    {
                        formContent.Add(new StringContent(item.Value.ToString()), item.Key);
                    }
                    request.Content = formContent;
                }
                var response = await client.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                var edge = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .FirstOrDefaultAsync(e => e.SourceId == node.Id);

                if (!response.IsSuccessStatusCode)
                {
                    // return null;
                    return edge?.Targets.FirstOrDefault()?.TargetNodeId;
                }
                return edge?.Targets.FirstOrDefault()?.TargetNodeId;
            }
            catch (HttpRequestException ex)
            {
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private string[]? ResolveVariableModelsToStringArray(List<VariableModel>? variableModels, Contacts contact)
        {
            if (variableModels == null || !variableModels.Any())
                return null;

            // Use StringHelper to get contact properties as dictionary
            var contactDict = StringHelper.GetPropertyNamesAndValues(contact);

            var resolvedValues = new List<string>();
            foreach (var variable in variableModels)
            {
                var resolvedValue = ResolveVariableUsingStringHelper(variable, contactDict);
                resolvedValues.Add(resolvedValue);
            }
            return resolvedValues.ToArray();
        }

        private List<string>? ResolveVariableModelsToStringList(List<VariableModel>? variableModels, Contacts contact)
        {
            if (variableModels == null || !variableModels.Any())
                return null;
            var contactDict = StringHelper.GetPropertyNamesAndValues(contact);

            var resolvedValues = new List<string>();
            foreach (var variable in variableModels)
            {
                var resolvedValue = ResolveVariableUsingStringHelper(variable, contactDict);
                resolvedValues.Add(resolvedValue);
            }
            return resolvedValues;
        }

        private string ResolveVariableUsingStringHelper(VariableModel? variableModel, Dictionary<string, object> contactDict)
        {
            if (variableModel == null)
                return string.Empty;

            try
            {
                // Use StringHelper pattern: try contact property first, then fallback
                if (!string.IsNullOrEmpty(variableModel.Value) && contactDict.TryGetValue(variableModel.Value, out var value))
                {
                    if (value != null && !string.IsNullOrEmpty(value.ToString()))
                    {
                        // Special handling for phone number like in existing code
                        if (variableModel.Value.Equals("Contact", StringComparison.OrdinalIgnoreCase) && contactDict.TryGetValue("CountryCode", out var countryCode))
                        {
                            return $"{countryCode}{value}";
                        }
                        return value.ToString();
                    }
                }
                return variableModel.FallbackValue ?? "Unknown";
            }
            catch (Exception ex)
            {
                return variableModel.FallbackValue ?? "Unknown";
            }
        }

        private string ResolveVariableModelToString(VariableModel? variableModel, Contacts contact)
        {
            if (variableModel == null)
                return string.Empty;
            var contactDict = StringHelper.GetPropertyNamesAndValues(contact);
            return ResolveVariableUsingStringHelper(variableModel, contactDict);
        }

        private async Task<Guid?> ProcessTemplateNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var template = new TemplateModel();
                template = node.PayloadModel.Template;

                if (template == null)
                {
                    return null;
                }

                if (!string.IsNullOrEmpty(textMessage))
                {

                    await CancelTimeoutIfNeeded(contacts, node.Id, "Template");

                    var edges = await _dbContext.WorkflowEdges.Include(e => e.Targets).Where(e => e.SourceId == node.Id).ToListAsync();

                    if (!edges.Any())
                    {
                        return null;
                    }

                    var targets = edges.SelectMany(e => e.Targets).ToList();

                    if (!targets.Any())
                    {
                        return null;
                    }

                    if (targets.Count == 1)
                    {
                        return targets[0].TargetNodeId;

                    }
                    if (targets.Any())
                    {
                        var nextNodeId = targets.FirstOrDefault(i => i.Condition == null).TargetNodeId;

                        return nextNodeId;
                    }


                }

                var business = await _dbContext.BusinessDetailsMetas.FirstOrDefaultAsync(b => b.BusinessId == contacts.BusinessId.ToString());
                if (business == null)
                {
                    return null;
                }

                var templateDetails = (await _genericRepository.GetByObjectAsync<Template>(new Dictionary<string, object> { { "BusinessId", business.BusinessId }, { "TemplateId", template.TemplateId ?? Guid.Empty } }, "Templates")).FirstOrDefault();
                if (templateDetails == null)
                {
                    return null;
                }

                var phoneNumber = $"{contacts.CountryCode}{contacts.Contact}";

                if (templateDetails.MediaType == MediaType.CAROUSEL)
                {
                    if (!template.IsScheduleReminder)
                    {
                        await SendCarouselReminderTemplate(contacts, business, template);
                    }
                    if (template.IsScheduleReminder)
                    {
                        await ScheduleReminder(contacts, template);
                    }
                    if (template.IsEnbaleSetTimeOut)
                    {
                        await HandleTimeout(node, contacts, template.IsEnbaleSetTimeOut, template.TimeOut, "Template");
                    }

                }
                else
                {
                    if (!template.IsScheduleReminder)
                    {
                        await SendRegularReminderTemplate(contacts, business, template);
                    }

                    if (template.IsScheduleReminder)
                    {
                        await ScheduleReminder(contacts, template);
                    }
                    if (template.IsEnbaleSetTimeOut)
                    {
                        await HandleTimeout(node, contacts, template.IsEnbaleSetTimeOut, template.TimeOut, "Template");
                    }
                }

                //var (isTrue, nextNodeIds) = await IsNextNodeIsShedulerRemainder(contacts, node.Id);
                // if(isTrue)
                // {
                //    return nextNodeIds;
                // }


                return null;
            }
            catch (Exception ex)
            {
                await _logHistoryService.SaveErrorLogHistoryAsyn("ProcessTemplateNodeAsync", null, $"Processing template for contact: {contacts.ContactId}", ex.Message, ex.StackTrace);
                return null;
            }
        }

        private async Task<Guid?> ProcessConditionNodeAsync(WorkflowNode node, Contacts contacts, string? textMessage)
        {
            try
            {
                var condition = node.PayloadModel?.Condition;
                if (condition == null)
                {
                    return null;
                }
                var previousResponses = await _customerWorkflowTrackerRepository.GetTrackerByWorkflowAsync(contacts.ContactId, contacts.WorkflowId);
                if (previousResponses == null || !previousResponses.Any())
                {
                    return null;
                }

                var matchingTracker = previousResponses
                                    .FirstOrDefault(t => t.AttributeName != null &&
                                     t.AttributeName.Equals(condition.Attribute, StringComparison.OrdinalIgnoreCase));

                if (matchingTracker == null)
                {

                    matchingTracker = previousResponses
                        .OrderByDescending(t => t.CreatedAt)
                        .FirstOrDefault(t => t.NodeType == NodeType.InteractiveMessage && !string.IsNullOrEmpty(t.CustomerReponse));
                }
                var edges = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .Where(e => e.SourceId == node.Id)
                    .ToListAsync();

                if (!edges.Any())
                {
                    return null;
                }

                var allTargets = edges.SelectMany(e => e.Targets).ToList();

                var conditionButtons = condition.Buttons;
                if (conditionButtons != null && conditionButtons.Any())
                {
                    var buttonTargetMap = new Dictionary<string, WorkflowEdgeTargetNode>();
                    foreach (var target in allTargets)
                    {
                        var sourceHandle = edges.FirstOrDefault(e => e.Targets.Contains(target))?.SourceHandle;
                        if (sourceHandle != null && sourceHandle.Contains("conditionButtonId-"))
                        {
                            var buttonId = sourceHandle.Split("conditionButtonId-").Last();
                            buttonTargetMap[buttonId] = target;
                        }
                    }

                    var trueButton = conditionButtons.FirstOrDefault(b => b.Name.Contains("True", StringComparison.OrdinalIgnoreCase));
                    var falseButton = conditionButtons.FirstOrDefault(b => b.Name.Contains("False", StringComparison.OrdinalIgnoreCase));

                    if (trueButton != null && falseButton != null &&
                        buttonTargetMap.ContainsKey(trueButton.Id) &&
                        buttonTargetMap.ContainsKey(falseButton.Id))
                    {
                        bool conditionResult = false;

                        if (matchingTracker != null)
                        {
                            switch (condition.Operator)
                            {
                                case ConditionOperator.Equals:
                                    conditionResult = string.Equals(matchingTracker.CustomerReponse, condition.Value, StringComparison.OrdinalIgnoreCase);
                                    break;
                                case ConditionOperator.NotEquals:
                                    conditionResult = !string.Equals(matchingTracker.CustomerReponse, condition.Value, StringComparison.OrdinalIgnoreCase);
                                    break;
                                case ConditionOperator.GreaterThan:
                                    conditionResult = double.TryParse(matchingTracker.CustomerReponse, out double inputNum) &&
                                                    double.TryParse(condition.Value, out double conditionNum) &&
                                                    inputNum > conditionNum;
                                    break;
                                case ConditionOperator.LessThan:
                                    conditionResult = double.TryParse(matchingTracker.CustomerReponse, out double inputNum2) &&
                                                    double.TryParse(condition.Value, out double conditionNum2) &&
                                                    inputNum2 < conditionNum2;
                                    break;
                                case ConditionOperator.Contains:
                                    conditionResult = matchingTracker.CustomerReponse?.Contains(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                case ConditionOperator.StartsWith:
                                    conditionResult = matchingTracker.CustomerReponse?.StartsWith(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                case ConditionOperator.EndsWith:
                                    conditionResult = matchingTracker.CustomerReponse?.EndsWith(condition.Value, StringComparison.OrdinalIgnoreCase) ?? false;
                                    break;
                                default:
                                    conditionResult = false;
                                    break;
                            }
                        }

                        var targetButton = conditionResult ? trueButton : falseButton;
                        var targetNodeId = buttonTargetMap[targetButton.Id].TargetNodeId;
                        return targetNodeId;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private async Task ScheduleReminder(Contacts contact, TemplateModel template)
        {
            try
            {
                if (!template.TimeInMinutes.HasValue || template.TimeInMinutes.Value <= 0 || !contact.ScheduledAt.HasValue)
                {
                    return;
                }

                var scheduledDate = contact.ScheduledAt.Value;
                var timeInMinutes = template.TimeInMinutes.Value;

                DateTime scheduledDateUtc;
                if (scheduledDate.Kind == DateTimeKind.Utc)
                {
                    scheduledDateUtc = scheduledDate;
                }
                else
                {
                    scheduledDateUtc = scheduledDate.Kind == DateTimeKind.Local
                        ? scheduledDate.ToUniversalTime()
                        : DateTime.SpecifyKind(scheduledDate, DateTimeKind.Utc);
                }

                var reminderTime = scheduledDateUtc.AddMinutes(-timeInMinutes);
                var now = DateTime.UtcNow;

                if (reminderTime <= now)
                {
                    _logger.LogDebug("Reminder time {ReminderTime} is in the past, skipping for contact {ContactId}",
                        reminderTime, contact.ContactId);
                    return;
                }

                DateTimeOffset reminderTimeOffset = StringHelper.ConvertToDateTimeOffset(reminderTime);

                var parameters = new
                {
                    contactId = contact.ContactId,
                    templateJson = JsonConvert.SerializeObject(template)
                };
                var jobId = await ScheduleJobAsync("ExecuteUnifiedReminderTemplateJob", parameters, reminderTimeOffset);

                contact.DelayResponseJobID = jobId;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling reminder for contact {ContactId}, template {TemplateId}, scheduled time {ScheduledTime}",
                    contact.ContactId, template.TemplateId, contact.ScheduledAt);
            }
        }

        public async Task ExecuteUnifiedReminderTemplateJob(Guid contactId, string templateModelJson)
        {
            try
            {
                _logger.LogInformation("ExecuteUnifiedReminderTemplateJob executing at UTC: {UtcNow}, Local: {LocalNow} for contact {ContactId}",
                    DateTime.UtcNow, DateTime.Now, contactId);
                TemplateModel? template = null;
                try
                {
                    template = JsonConvert.DeserializeObject<TemplateModel>(templateModelJson);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deserializing template model for contact {ContactId}", contactId);
                    return;
                }

                if (template == null)
                {
                    _logger.LogWarning("Template model is null after deserialization for contact {ContactId}", contactId);
                    return;
                }

                var contact = await _dbContext.Contacts.FirstOrDefaultAsync(c => c.ContactId == contactId);
                if (contact == null)
                {
                    _logger.LogWarning("Contact {ContactId} not found for reminder", contactId);
                    return;
                }

                var business = await _dbContext.BusinessDetailsMetas.FirstOrDefaultAsync(b => b.BusinessId == contact.BusinessId.ToString());
                if (business == null)
                {
                    _logger.LogWarning("Business not found for contact {ContactId}", contactId);
                    return;
                }

                bool isCarouselTemplate = template.CarouselVariables != null && template.CarouselVariables.Any();

                if (isCarouselTemplate)
                {
                    await SendCarouselReminderTemplate(contact, business, template);
                }
                else
                {
                    await SendRegularReminderTemplate(contact, business, template);
                }

                _logger.LogInformation("Successfully sent {TemplateType} reminder template to contact {ContactId}",
                    isCarouselTemplate ? "carousel" : "regular", contactId);

                contact.DelayResponseJobID = null;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();

                await MoveToNextNodeAndContinueWorkflow(contact);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing unified reminder template job for contact {ContactId}", contactId);
                throw;
            }
        }

        private async Task SendRegularReminderTemplate(Contacts contact, BusinessDetailsMeta business, TemplateModel template)
        {
            try
            {
                var resolvedBodyVariables = ResolveVariableModelsToStringList(template.BodyVariableValues, contact);
                var resolvedHeaderVariable = ResolveVariableModelToString(template.HeaderValue, contact);

                var templateRequest = new TemplateRequestDto
                {
                    TemplateName = template.TemplateName,
                    CountryCode = contact.CountryCode,
                    Contact = contact.Contact,
                    Name = contact.Name,
                    HeaderValue = resolvedHeaderVariable,
                    BodyVariableValues = resolvedBodyVariables
                };
                //var templates = (await _genericRepository.GetByObjectAsync<Template>(
                //    new Dictionary<string, object>
                //    {
                //                    { "BusinessId", contact.BusinessId },
                //                    { "TemplateId", templateModel.TemplateId ?? Guid.Empty }
                //    },
                //    "Templates")).FirstOrDefault();

                //var fullContact = $"{contact.CountryCode}{contact.Contact}".Replace("+", "");
                //var response =  await _metaApiService.SendTemplateAsync(business.BusinessId,templates.LanguageCode,fullContact, templates.TemplateName, resolvedBodyVariables, templates.MediaType, resolvedHeaderVariable, business.Token, business.PhoneNumberID, business.WhatsAppBusinessAccountID);

                var templateService = _serviceProvider.GetRequiredService<ITemplate>();
                var companyId = Guid.Parse(business.BusinessId);
                var conversion = await templateService.GetSendTemplateAsync(business.BusinessId, companyId, templateRequest);

                if (conversion.WhatsAppMessageId == null || conversion.WhatsAppMessageId.IsNullOrWhiteSpace())
                {
                    contact.WorkFlowNodeId = null;
                    contact.WorkflowId = null;
                    _dbContext.Contacts.Update(contact);
                    await _dbContext.SaveChangesAsync();
                }

            }
            catch (Exception ex)
            {
                contact.WorkFlowNodeId = null;
                contact.WorkflowId = null;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();
            }

        }

        private async Task SendCarouselReminderTemplate(Contacts contact, BusinessDetailsMeta business, TemplateModel template)
        {
            try
            {
                var resolvedCarouselVariables = new List<CarouselCardVariableDto>();

                if (template.CarouselVariables != null)
                {
                    foreach (var carouselVar in template.CarouselVariables)
                    {
                        var resolvedBodyCarouselVariables = ResolveVariableModelsToStringList(carouselVar.BodyCarouselVariableValues, contact);
                        var resolvedRedirectUrlVariables = ResolveVariableModelsToStringList(carouselVar.RedirectUrlVariableValues, contact);

                        resolvedCarouselVariables.Add(new CarouselCardVariableDto
                        {
                            BodyCarouselVariableValues = resolvedBodyCarouselVariables?.ToArray(),
                            RedirectUrlVariableValues = resolvedRedirectUrlVariables?.ToArray(),
                            MediaUrl = carouselVar.MediaUrl
                        });
                    }
                }

                var resolvedBodyVariables = ResolveVariableModelsToStringList(template.BodyVariableValues, contact);

                var carouselRequest = new SendCarouselTemplateDto
                {
                    BusinessId = business.BusinessId,
                    UserId = contact.UserId ?? Guid.Empty,
                    TemplateId = template.TemplateId ?? Guid.Empty,
                    Contact = new List<string> { contact.Contact },
                    BodyVariableValues = resolvedBodyVariables?.ToArray(),
                    CarouselVariables = resolvedCarouselVariables
                };

                var templateService = _serviceProvider.GetRequiredService<ITemplate>();

                var response = await templateService.SendCarouselTemplateAsync(carouselRequest);

                if (!response.IsSuccessStatusCode)
                {
                    contact.WorkFlowNodeId = null;
                    contact.WorkflowId = null;
                    _dbContext.Contacts.Update(contact);
                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                contact.WorkFlowNodeId = null;
                contact.WorkflowId = null;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();
            }
        }



        private async Task MoveToNextNodeAndContinueWorkflow(Contacts contact)
        {
            try
            {
                if (contact.WorkFlowNodeId == null)
                {
                    return;
                }

                var currentNode = await _dbContext.WorkflowNodes.FirstOrDefaultAsync(n => n.Id == contact.WorkFlowNodeId);
                if (currentNode == null)
                {
                    return;
                }
                var edges = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .Where(e => e.SourceId == currentNode.Id)
                    .ToListAsync();

                if (!edges.Any())
                {
                    await MarkWorkflowCompletedAsync(contact);
                    return;
                }

                var targets = edges.SelectMany(e => e.Targets).ToList();
                if (!targets.Any())
                {
                    await MarkWorkflowCompletedAsync(contact);
                    return;
                }

                var nextNodeId = targets.First().TargetNodeId;
                contact.WorkFlowNodeId = nextNodeId;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();

                await ProcessWorkflowAsync(contact, null, false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving to next node and continuing workflow for contact {ContactId}, current node {NodeId}",
                    contact.ContactId, contact.WorkFlowNodeId);
            }
        }
        public async Task<int> CleanupScheduledJobsForNode(Guid nodeId)
        {
            try
            {
                var contactsWithJobs = await _dbContext.Contacts
                    .Where(c => !string.IsNullOrEmpty(c.DelayResponseJobID) && c.WorkFlowNodeId == nodeId)
                    .ToListAsync();

                int cleanedCount = 0;

                foreach (var contact in contactsWithJobs)
                {
                    if (await CleanupJobForContact(contact))
                    {
                        cleanedCount++;
                    }
                }

                _logger.LogInformation("Successfully cleaned up {Count} scheduled jobs for node {NodeId}",
                    cleanedCount, nodeId);

                return cleanedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up scheduled jobs for node {NodeId}", nodeId);
                return 0;
            }
        }

        public async Task<int> CleanupScheduledJobsForWorkflow(Guid workflowId)
        {
            try
            {

                var contactsWithJobs = await _dbContext.Contacts
                    .Where(c => !string.IsNullOrEmpty(c.DelayResponseJobID) && c.WorkflowId == workflowId)
                    .ToListAsync();

                int cleanedCount = 0;

                foreach (var contact in contactsWithJobs)
                {
                    if (await CleanupJobForContact(contact))
                    {
                        cleanedCount++;
                    }
                }

                return cleanedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up scheduled jobs for workflow {WorkflowId}", workflowId);
                return 0;
            }
        }
        private async Task<bool> CleanupJobForContact(Contacts contact)
        {
            try
            {
                if (string.IsNullOrEmpty(contact.DelayResponseJobID))
                {
                    return false;
                }
                bool jobDeleted = false;

                jobDeleted = await DeleteJobAsync(contact.DelayResponseJobID);

                contact.DelayResponseJobID = null;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private async Task HandleTimeout(WorkflowNode node, Contacts contact, bool isEnbaleSetTimeOut, int? timeOut, string nodeType)
        {
            try
            {
                if (!isEnbaleSetTimeOut || !timeOut.HasValue || timeOut.Value <= 0)
                {
                    Console.WriteLine($"⏭️ {nodeType} node {node.Id} has no timeout configured, skipping timeout setup");
                    return;
                }

                if (!string.IsNullOrEmpty(contact.DelayResponseJobID))
                {
                    return;
                }

                var timeoutMinutes = timeOut.Value;
                var timeoutTime = DateTime.UtcNow.AddMinutes(timeoutMinutes);
                //  var timeoutTimeOffset = new DateTimeOffset(timeoutTime, TimeSpan.Zero);
                DateTimeOffset timeoutTimeOffset = StringHelper.ConvertToDateTimeOffset(timeoutTime);

                var parameters = new
                {
                    contactId = contact.ContactId,
                    nodeId = node.Id,
                    nodeType = nodeType
                };
                var jobId = await ScheduleJobAsync("ExecuteTimeoutJobAsync", parameters, timeoutTimeOffset);

                contact.DelayResponseJobID = jobId;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();

                Console.WriteLine($"Scheduled {nodeType} timeout for contact {contact.ContactId} on node {node.Id} in {timeoutMinutes} minutes (Job ID: {jobId})");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling timeout for {NodeType} node {NodeId} and contact {ContactId}",
                    nodeType, node.Id, contact.ContactId);
            }
        }
        private async Task CancelTimeoutIfNeeded(Contacts contact, Guid nodeId, string nodeType)
        {
            if (!string.IsNullOrEmpty(contact.DelayResponseJobID))
            {
                await DeleteJobAsync(contact.DelayResponseJobID);
                contact.DelayResponseJobID = null;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();
                Console.WriteLine($"User responded to {nodeType} node {nodeId}, cancelled timeout for contact {contact.ContactId}");
            }
        }


        public async Task ExecuteTimeoutJobAsync(Guid contactId, Guid nodeId, string nodeType)
        {
            try
            {
                _logger.LogInformation("ExecuteTimeoutJob executing for contact {ContactId} on {NodeType} node {NodeId}",
                    contactId, nodeType, nodeId);

                var contact = await _dbContext.Contacts.FirstOrDefaultAsync(c => c.ContactId == contactId);
                if (contact == null)
                {
                    _logger.LogWarning("Contact {ContactId} not found for {NodeType} timeout", contactId, nodeType);
                    return;
                }

                var node = await _dbContext.WorkflowNodes.FirstOrDefaultAsync(n => n.Id == nodeId);
                if (node == null)
                {
                    _logger.LogWarning("Node {NodeId} not found for {NodeType} timeout", nodeId, nodeType);
                    return;
                }

                if (contact.WorkFlowNodeId != nodeId)
                {
                    _logger.LogInformation("Contact {ContactId} has moved from {NodeType} node {NodeId}, skipping timeout",
                        contactId, nodeType, nodeId);
                    return;
                }

                contact.DelayResponseJobID = null;
                _dbContext.Contacts.Update(contact);
                await _dbContext.SaveChangesAsync();
                await TriggerNextNodeAfterTimeout(contact, nodeId, nodeType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing timeout job for contact {ContactId} on {NodeType} node {NodeId}",
                    contactId, nodeType, nodeId);
                throw;
            }
        }

        private async Task TriggerNextNodeAfterTimeout(Contacts contact, Guid nodeId, string nodeType)
        {
            try
            {
                var edges = await _dbContext.WorkflowEdges
                    .Include(e => e.Targets)
                    .Where(e => e.SourceId == nodeId)
                    .ToListAsync();

                if (edges.Any())
                {
                    var targets = edges.SelectMany(e => e.Targets).ToList();
                    if (targets.Any())
                    {
                        var nextNodeId = targets.FirstOrDefault(i => i.Condition == "timeout").TargetNodeId;
                        var nextNode = await _dbContext.WorkflowNodes.FirstOrDefaultAsync(n => n.Id == nextNodeId);

                        contact.WorkFlowNodeId = nextNodeId;
                        _dbContext.Contacts.Update(contact);
                        await _dbContext.SaveChangesAsync();

                        await ProcessWorkflowAsync(contact, null, false);


                    }
                    else
                    {

                        await MarkWorkflowCompletedAsync(contact);
                    }
                }
                else
                {
                    _logger.LogWarning("No timeout edges configured for {NodeType} node {NodeId}", nodeType, nodeId);
                    await MarkWorkflowCompletedAsync(contact);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering next node after timeout for {NodeType} node {NodeId}", nodeType, nodeId);
                throw;
            }
        }
        //private async Task<(bool, Guid?)> IsNextNodeIsShedulerRemainder(Contacts contact, Guid nodeId)
        //{
        //    try
        //    {
        //        var edges = await _dbContext.WorkflowEdges
        //            .Include(e => e.Targets)
        //            .Where(e => e.SourceId == nodeId)
        //            .ToListAsync();

        //        var targets = edges
        //            .Where(e => e.Targets != null)
        //            .SelectMany(e => e.Targets)
        //            .ToList();

        //       var nextNodeId = targets.Select(i=>i.TargetNodeId).FirstOrDefault();

        //        if (nextNodeId == null)
        //        {
        //            return (false, null);
        //        }

        //        var nextNode = await _dbContext.WorkflowNodes
        //            .FirstOrDefaultAsync(n =>
        //                n.Id == nextNodeId &&
        //                n.PayloadModel != null &&
        //                n.PayloadModel.Template != null &&
        //                n.PayloadModel.Template.IsScheduleReminder == true);

        //        if (nextNode == null)
        //        {
        //            return (false, null);
        //        }

        //        return (true, nextNodeId);
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine(ex);
        //        return (false, null);
        //    }
        //}

        #region Function App Job Management Methods

        private async Task<string> ScheduleJobAsync(string methodName, object parameters, DateTimeOffset scheduleTime)
        {
            try
            {
                var functionUrl = _environmentService.IsDevelopment
                    ? _configuration["FunctionSettings:Dev_ScheduleJobUrl"]
                    : _configuration["FunctionSettings:Prod_ScheduleJobUrl"];

                if (string.IsNullOrWhiteSpace(functionUrl))
                {
                    throw new InvalidOperationException("Function URL is not configured for job scheduling.");
                }

                // Create workflow job data to be stored in JsonData
                var workflowJobData = new
                {
                    MethodName = methodName,
                    Parameters = parameters
                };

                // Create InputPayload format for existing JobScheduleRequest endpoint
                var payload = new
                {
                    Id = Guid.NewGuid(),
                    JsonData = JsonConvert.SerializeObject(workflowJobData),
                    Type = "WorkflowJob",
                    ScheduledTime = scheduleTime.DateTime,
                    IsDevelopment = _environmentService.IsDevelopment
                };

                var client = _httpClientFactory.CreateClient();
                var jsonPayload = JsonConvert.SerializeObject(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(functionUrl, content);
                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadAsStringAsync();
                var jobResponse = JsonConvert.DeserializeObject<dynamic>(result);

                // Extract job ID from response
                return jobResponse?.instanceId?.ToString() ?? Guid.NewGuid().ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling workflow job via Function App: {MethodName}", methodName);
                throw;
            }
        }

        private async Task<bool> DeleteJobAsync(string jobId)
        {
            try
            {
                if (string.IsNullOrEmpty(jobId))
                    return false;

                var functionUrl = _environmentService.IsDevelopment
                    ? _configuration["FunctionSettings:Dev_TerminateJobUrl"]
                    : _configuration["FunctionSettings:Prod_TerminateJobUrl"];

                if (string.IsNullOrWhiteSpace(functionUrl))
                {
                    throw new InvalidOperationException("Function URL is not configured for job termination.");
                }

                var payload = new { JobId = jobId };

                var client = _httpClientFactory.CreateClient();
                var jsonPayload = JsonConvert.SerializeObject(payload);
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                var response = await client.PostAsync(functionUrl, content);

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting job via Function App: {JobId}", jobId);
                return false;
            }
        }

        #endregion

    }
}
